# Simple-RAG-Assistant

基于大语言模型的检索增强生成（RAG）知识库问答系统。结合先进的向量检索和大语言模型，为用户提供精准的知识库问答体验。

## 项目特点

- 🔍 **知识库检索**：基于向量数据库的相似性检索，快速定位相关信息
- 🧠 **智能问答**：结合大语言模型理解能力，生成连贯、准确的回答
- 📚 **知识库管理**：支持自定义知识库上传、构建和切换
- 🔄 **上下文记忆**：支持多轮对话，保持对话连贯性
- 🎛️ **灵活配置**：可自由切换不同语言模型、嵌入模型及参数设置
- 🌐 **多模型支持**：支持ChatGLM、Spark、GPT等多种大语言模型
- 🖥️ **友好界面**：简洁易用的Web界面，代码块美观展示

## 项目结构

```
Simple-RAG-Assistant/
├── backend/            # 后端代码
│   ├── api.py          # FastAPI应用主入口
│   ├── run_api.py      # 启动API服务脚本
│   ├── database/       # 数据库操作相关代码
│   │   └── vector_db/  # 向量数据库
│   │       ├── default/ # 默认知识库
│   │       └── user/   # 用户上传知识库
│   ├── llm/            # 大语言模型调用模块
│   ├── embedding/      # 嵌入模型模块
│   └── qa_chain/       # 问答链实现
├── frontend/           # 前端代码
│   ├── ui/             # Vue前端项目
│   └── start.sh        # 前端启动脚本
├── resources/          # 资源文件
│   ├── figures/        # 图片资源
│   └── knowledge_db/   # 默认知识库原始文件
├── requirements.txt    # Python依赖
└── start.sh            # 项目启动脚本
```

## 环境要求

- Python 3.9+
- Node.js 14+
- npm 6+

## 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/Chaser-Lin/Simple-RAG-Assistant.git
cd Simple-RAG-Assistant
```

### 2. 安装依赖

#### 后端依赖

推荐使用conda创建虚拟环境：

```bash
# 创建虚拟环境
conda create -n rag-assistant python=3.9
conda activate rag-assistant

# 安装依赖
pip install -r requirements.txt
```

配置模型api key：
在.env文件配置对应的大模型api key

#### 前端依赖

```bash
cd frontend/ui
npm install
```

### 3. 启动项目

#### 方法一：一键启动（推荐）

在项目根目录执行：

```bash
./start.sh
```

此脚本会同时启动前端和后端服务。

#### 方法二：分别启动

**启动后端：**

```bash
cd backend
python run_api.py
```

**启动前端：**

```bash
cd frontend
./start.sh
```

或者直接启动前端开发服务器：

```bash
cd frontend/ui
npm run serve
```

### 4. 访问应用

前端界面：http://localhost:8080
后端API：http://localhost:8008

## 使用指南

1. **知识库管理**：
   - 点击界面上的"知识库"标签
   - 上传自己的文档（支持PDF、TXT、Markdown等格式）
   - 点击"处理知识库"构建向量数据库

2. **设置模型**：
   - 点击"设置"图标
   - 选择需要使用的大语言模型和嵌入模型
   - 调整参数（温度、相似度匹配数量等）

3. **开始对话**：
   - 在输入框中输入问题
   - 系统会从知识库中检索相关信息并生成回答
   - 支持连续提问，系统会保持对话上下文

## 系统配置

默认配置使用以下模型：
- 大语言模型：ChatGLM-std
- 嵌入模型：M3E
- 相似度匹配：3条
- 历史记忆：3轮对话

## 注意事项

- 首次处理知识库可能需要较长时间，请耐心等待
- 大文件处理可能消耗较多内存，请确保系统资源充足
- 默认使用开源模型，如需使用OpenAI等API，请在相应配置文件中设置API密钥
- 数据库文件存储在backend/database/vector_db目录下，包含默认知识库和用户知识库

## 技术栈

- 后端：FastAPI, LangChain, ChromaDB
- 前端：Vue.js, Element Plus
- 数据库：Chroma向量数据库
- 模型：支持多种开源和闭源大语言模型