#!/bin/bash

# 设置颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}启动 Simple-RAG-Assistant 项目...${NC}"

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "错误: 未找到Python命令，请确认已安装Python。"
    exit 1
fi

# 检查conda环境
if command -v conda &> /dev/null; then
    echo -e "${BLUE}检测到conda环境，建议使用 conda activate 激活适当的环境${NC}"
fi

# 启动后端服务
echo -e "${GREEN}启动后端API服务...${NC}"
cd backend
python run_api.py &
BACKEND_PID=$!
cd ..

# 等待后端服务启动
echo "等待后端服务启动..."
sleep 3

# 启动前端服务
echo -e "${GREEN}启动前端服务...${NC}"
cd frontend
./start.sh &
FRONTEND_PID=$!

# 显示访问信息
echo -e "\n${GREEN}=====================================================${NC}"
echo -e "${GREEN}Simple-RAG-Assistant 已启动${NC}"
echo -e "${BLUE}后端API运行于: http://localhost:8008${NC}"
echo -e "${BLUE}前端页面运行于: http://localhost:8080${NC}"
echo -e "${BLUE}数据库位置: backend/database/vector_db/${NC}"
echo -e "${GREEN}=====================================================${NC}"
echo -e "按 Ctrl+C 停止服务\n"

# 捕获SIGINT信号（Ctrl+C）
trap 'echo -e "\n${GREEN}正在停止服务...${NC}"; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' INT

# 等待子进程结束
wait 